<?xml version="1.0" encoding="UTF-8"?>

<!--
	OvenMediaEngine Server Configuration - Optimized for LLHLS Streaming

	CONFIGURATION SUMMARY:
	- Primary Input: RTMP on port 1935
	- Primary Output: LLHLS on port 8080 (TLS on 8443)
	- Active Quality: 1080p (1920x1080, 5Mbps, 30fps)
	- Future Qualities: 2K and 4K profiles (commented out for easy activation)
	- WebRTC: Enabled for compatibility (ports 3333/3334)
	- Optimizations: Unnecessary providers/publishers commented out for performance

	TO ENABLE HIGHER QUALITIES:
	1. Uncomment the desired profile (2k_profile or 4k_profile) in OutputProfiles section
	2. Restart OvenMediaEngine

	STREAMING URLS:
	- LLHLS: http://your-server:8080/app/stream_name/llhls.m3u8
	- WebRTC: ws://your-server:3333/app/stream_name
	- RTMP Input: rtmp://your-server:1935/app/stream_name
-->

<Server version="8">
	<Name>OvenMediaEngine</Name>
	<!-- Host type (origin/edge) -->
	<Type>origin</Type>
	<!-- Specify IP address to bind ("*" means all IPv4 IPs, "::" means all IPv6 IPs) -->
	<!-- Uncomment the line below to enable IPv6 -->
	<!-- <IP>::</IP> -->
	<IP>*</IP>
	<PrivacyProtection>false</PrivacyProtection>

	<!-- 
	To get the public IP address(mapped address of stun) of the local server. 
	This is useful when OME cannot obtain a public IP from an interface, such as AWS or docker environment. 
	If this is successful, you can use ${PublicIP} in your settings.
	-->
	<StunServer>stun.ovenmediaengine.com:13478</StunServer>

	<Modules>
		<!-- 
		Currently OME only supports h2 like all browsers do. Therefore, HTTP/2 only works on TLS ports.			
		-->
		<HTTP2>
			<Enable>true</Enable>
		</HTTP2>

		<LLHLS>
			<Enable>true</Enable>
		</LLHLS>

		<!-- P2P works only in WebRTC and is experiment feature -->
		<P2P>
			<!-- disabled by default -->
			<Enable>false</Enable>
			<MaxClientPeersPerHostPeer>2</MaxClientPeersPerHostPeer>
		</P2P>
	</Modules>

	<!-- Settings for the ports to bind -->
	<Bind>
		<!-- Enable this configuration if you want to use API Server -->
		<!--
		<Managers>
			<API>
				<Port>8081</Port>
				<TLSPort>8082</TLSPort>
				<WorkerCount>1</WorkerCount>
			</API>
		</Managers>
		-->

		<Providers>
			<!-- Pull providers -->
			<RTSPC>
				<WorkerCount>1</WorkerCount>
			</RTSPC>
			<!-- OVT provider commented out for LLHLS optimization -->
			<!--
			<OVT>
				<WorkerCount>1</WorkerCount>
			</OVT>
			-->
			<!-- Push providers -->
			<RTMP>
				<Port>1935</Port>
				<WorkerCount>1</WorkerCount>
			</RTMP>
			<!-- SRT provider commented out for LLHLS optimization -->
			<!--
			<SRT>
				<Port>9999</Port>
				<WorkerCount>1</WorkerCount>
			</SRT>
			-->
			<!-- MPEGTS provider commented out for LLHLS optimization -->
			<!--
			<MPEGTS>
				<Port>4000/udp</Port>
			</MPEGTS>
			-->
			<WebRTC>
				<Signalling>
					<Port>3333</Port>
					<TLSPort>3334</TLSPort>
					<WorkerCount>1</WorkerCount>
				</Signalling>

				<IceCandidates>
					<!-- Uncomment the line below to use IPv6 ICE Candidate -->
					<!-- <IceCandidate>[::]:10000-10004/udp</IceCandidate> -->
					<IceCandidate>*:10000-10004/udp</IceCandidate>

					<!-- Uncomment the line below to use a link local address when specifying an address with IPv6 wildcard (::) -->
					<!-- <EnableLinkLocalAddress>true</EnableLinkLocalAddress> -->

					<!-- 
						If you want to stream WebRTC over TCP, specify IP:Port for TURN server.
						This uses the TURN protocol, which delivers the stream from the built-in TURN server to the player's TURN client over TCP. 
					-->
					<TcpRelay>*:3478</TcpRelay>
					<!-- TcpForce is an option to force the use of TCP rather than UDP in WebRTC streaming. (You can omit ?transport=tcp accordingly.) If <TcpRelay> is not set, playback may fail. -->
					<TcpForce>true</TcpForce>
					<TcpRelayWorkerCount>1</TcpRelayWorkerCount>
				</IceCandidates>
			</WebRTC>
		</Providers>

		<Publishers>
			<!-- OVT publisher commented out for LLHLS optimization -->
			<!--
			<OVT>
				<Port>9000</Port>
				<WorkerCount>1</WorkerCount>
			</OVT>
			-->
			<LLHLS>
				<!--
				OME only supports h2, so LLHLS works over HTTP/1.1 on non-TLS ports.
				LLHLS works with higher performance over HTTP/2,
				so it is recommended to use a TLS port.
				Port changed to 8080 for dedicated LLHLS streaming.
				-->
				<Port>8080</Port>
				<!-- If you want to use TLS, specify the TLS port -->
				<TLSPort>8443</TLSPort>
				<WorkerCount>1</WorkerCount>
			</LLHLS>
			<WebRTC>
				<Signalling>
					<Port>3333</Port>
					<TLSPort>3334</TLSPort>
					<WorkerCount>1</WorkerCount>
				</Signalling>
				<IceCandidates>
					<!-- Uncomment the line below to use IPv6 ICE Candidate -->
					<!-- <IceCandidate>[::]:10000-10004/udp</IceCandidate> -->
					<IceCandidate>*:10000-10004/udp</IceCandidate>
					
					<!-- Uncomment the line below to use a link local address when specifying an address with IPv6 wildcard (::) -->
					<!-- <EnableLinkLocalAddress>true</EnableLinkLocalAddress> -->

					<!-- 
						If you want to stream WebRTC over TCP, specify IP:Port for TURN server.
						This uses the TURN protocol, which delivers the stream from the built-in TURN server to the player's TURN client over TCP. 
					-->
					<TcpRelay>*:3478</TcpRelay>
					<!-- TcpForce is an option to force the use of TCP rather than UDP in WebRTC streaming. (You can omit ?transport=tcp accordingly.) If <TcpRelay> is not set, playback may fail. -->
					<TcpForce>true</TcpForce>
					<TcpRelayWorkerCount>1</TcpRelayWorkerCount>
				</IceCandidates>
			</WebRTC>
			<!-- SRT publisher commented out for LLHLS optimization -->
			<!--
			<SRT>
				<Port>9998</Port>
				<WorkerCount>1</WorkerCount>
			</SRT>
			-->
		</Publishers>
	</Bind>

	<!--
		Enable this configuration if you want to use API Server
		
		<AccessToken> is a token for authentication, and when you invoke the API, you must put "Basic base64encode(<AccessToken>)" in the "Authorization" header of HTTP request.
		For example, if you set <AccessToken> to "ome-access-token", you must set "Basic b21lLWFjY2Vzcy10b2tlbg==" in the "Authorization" header.
	-->
	<!--
	<Managers>
		<Host>
			<Names>
				<Name>*</Name>
			</Names>
			<TLS>
				<CertPath>path/to/file.crt</CertPath>
				<KeyPath>path/to/file.key</KeyPath>
				<ChainCertPath>path/to/file.crt</ChainCertPath>
			</TLS>
		</Host>
		<API>
			<AccessToken>ome-access-token</AccessToken>

			<CrossDomains>
				<Url>*.airensoft.com</Url>
				<Url>http://*.sub-domain.airensoft.com</Url>
				<Url>http?://airensoft.*</Url>
			</CrossDomains>
		</API>
	</Managers>
	-->

	<!--
		Refer https://airensoft.gitbook.io/ovenmediaengine/alert
	<Alert>
		<Url></Url>
		<SecretKey></SecretKey>
		<Timeout>3000</Timeout>
		<Rules>
			<Ingress>
				<MinBitrate></MinBitrate>
				<MaxBitrate></MaxBitrate>
				<MinFramerate></MinFramerate>
				<MaxFramerate></MaxFramerate>
				<MinWidth></MinWidth>
				<MinHeight></MinHeight>
				<MaxWidth></MaxWidth>
				<MaxHeight></MaxHeight>
				<MinSamplerate></MinSamplerate>
				<MaxSamplerate></MaxSamplerate>
				<LongKeyFrameInterval />
				<HasBFrames />
			</Ingress>
		</Rules>
	</Alert>
	-->

	<VirtualHosts>
		<!-- You can use wildcard like this to include multiple XMLs -->
		<VirtualHost include="VHost*.xml" />
		<VirtualHost>
			<Name>default</Name>
			<!--Distribution is a value that can be used when grouping the same vhost distributed across multiple servers. This value is output to the events log, so you can use it to aggregate statistics. -->
			<Distribution>ovenmediaengine.com</Distribution>

			<!-- Settings for multi ip/domain and TLS -->
			<Host>
				<Names>
					<!-- Host names
						<Name>stream1.airensoft.com</Name>
						<Name>stream2.airensoft.com</Name>
						<Name>*.sub.airensoft.com</Name>
						<Name>***********</Name>
					-->
					<Name>*</Name>
				</Names>
				<!--
				<TLS>
					<CertPath>path/to/file.crt</CertPath>
					<KeyPath>path/to/file.key</KeyPath>
					<ChainCertPath>path/to/file.crt</ChainCertPath>
				</TLS>
				-->
			</Host>

			<!-- 	
			Refer https://airensoft.gitbook.io/ovenmediaengine/signedpolicy
			<SignedPolicy>
				<PolicyQueryKeyName>policy</PolicyQueryKeyName>
				<SignatureQueryKeyName>signature</SignatureQueryKeyName>
				<SecretKey>aKq#1kj</SecretKey>

				<Enables>
					<Providers>rtmp,webrtc,srt</Providers>
					<Publishers>webrtc,llhls</Publishers>
				</Enables>
			</SignedPolicy>
			-->

			<!--
			AdmissionWebhooks configuration for stream lifecycle management
			Replaces nginx-rtmp on_publish and on_done webhooks
			Single endpoint handles both opening/closing events based on request.status
			-->
			<AdmissionWebhooks>
				<ControlServerUrl>http://**********:8081/v1/api/stream/live</ControlServerUrl>
				<SecretKey>ome-webhook-secret-2024</SecretKey>
				<Timeout>5000</Timeout>
				<Enables>
					<Providers>rtmp</Providers>
					<Publishers>rtmp</Publishers>
				</Enables>
			</AdmissionWebhooks>

			<!-- <Origins>
				<Properties>
					<NoInputFailoverTimeout>3000</NoInputFailoverTimeout>
					<UnusedStreamDeletionTimeout>60000</UnusedStreamDeletionTimeout>
				</Properties>
				<Origin>
					<Location>/app/stream</Location>
					<Pass>
						<Scheme>ovt</Scheme>
						<Urls><Url>origin.com:9000/app/stream_720p</Url></Urls>
					</Pass>
					<ForwardQueryParams>false</ForwardQueryParams>
				</Origin>
				<Origin>
					<Location>/app/</Location>
					<Pass>
						<Scheme>ovt</Scheme>
						<Urls><Url>origin.com:9000/app/</Url></Urls>
					</Pass>
				</Origin>
				<Origin>
					<Location>/edge/</Location>
					<Pass>
						<Scheme>ovt</Scheme>
						<Urls><Url>origin.com:9000/app/</Url></Urls>
					</Pass>
				</Origin>
			</Origins> -->

			<!--
			<OriginMapStore>
				In order to use OriginMap, you must enable OVT Publisher in Origin and OVT Provider in Edge.
				<RedisServer>
					<Host>***********60:6379</Host>
					<Auth>!@#ovenmediaengine</Auth>
				</RedisServer>
				
				This is only needed for the origin server and used to register the ovt address of the stream. 
				<OriginHostName>ome-dev.airensoft.com</OriginHostName>
			</OriginMapStore>
			-->

			<!-- Default CORS Settings -->
			<CrossDomains>
				<Url>*</Url>
			</CrossDomains>

			<!-- Settings for applications -->
			<Applications>
				<Application>
					<Name>app</Name>
					<!-- Application type (live/vod) -->
					<Type>live</Type>
					<OutputProfiles>
						<!-- Common setting for decoders. Decodes is optional. -->
						<Decodes>
							<!-- Number of threads for the decoder. -->
							<ThreadCount>2</ThreadCount>
							<!--
							By default, OME decodes all video frames.
							With OnlyKeyframes, only keyframes are decoded, massively improving performance.
							Thumbnails are generated only on keyframes, they may not generate at your requested fps!
							-->
							<OnlyKeyframes>false</OnlyKeyframes>
						</Decodes>

						<!-- Enable this configuration if you want to hardware acceleration using GPU -->
						<HWAccels>
							<Decoder>
									<Enable>false</Enable>
									<!--
										Setting for Hardware Modules.
										 - xma :Xilinx Media Accelerator
										 - qsv :Intel Quick Sync Video
										 - nv : Nvidia Video Codec SDK
										 - nilogan: Netint VPU

										You can use multiple modules by separating them with commas.
										For example, if you want to use xma and nv, you can set it as follows.

										<Modules>[ModuleName]:[DeviceId],[ModuleName]:[DeviceId],...</Modules>
										<Modules>xma:0,nv:0</Modules>
									-->
									<!-- <Modules>nv</Modules> -->
							</Decoder>
							<Encoder>
									<Enable>false</Enable>
									<!-- <Modules>nv</Modules> -->
							</Encoder>
						</HWAccels>

						<!-- ACTIVE PROFILE: 1080p for production streaming -->
						<OutputProfile>
							<Name>1080p_profile</Name>
							<OutputStreamName>${OriginStreamName}_1080p</OutputStreamName>

							<Playlist>
								<Name>1080p</Name>
								<FileName>master_1080p</FileName>
								<Options>
									<WebRtcAutoAbr>true</WebRtcAutoAbr>
									<HLSChunklistPathDepth>0</HLSChunklistPathDepth>
									<EnableTsPackaging>true</EnableTsPackaging>
								</Options>
								<RenditionTemplate>
									<Name>1080p</Name>
									<VideoTemplate>
										<EncodingType>encoded</EncodingType>
									</VideoTemplate>
									<AudioTemplate>
										<EncodingType>encoded</EncodingType>
									</AudioTemplate>
								</RenditionTemplate>
							</Playlist>

							<Encodes>
								<Video>
									<Name>video_1080p</Name>
									<Codec>h264</Codec>
									<Bitrate>5000000</Bitrate>
									<Width>1920</Width>
									<Height>1080</Height>
									<Framerate>30</Framerate>
									<KeyFrameInterval>30</KeyFrameInterval>
									<BFrames>0</BFrames>
									<Preset>faster</Preset>
								</Video>
								<Audio>
									<Name>aac_audio_1080p</Name>
									<Codec>aac</Codec>
									<Bitrate>128000</Bitrate>
									<Samplerate>48000</Samplerate>
									<Channel>2</Channel>
								</Audio>
							</Encodes>
						</OutputProfile>

					<!-- FUTURE PROFILE: 2K for high-quality streaming (commented out for easy activation) -->
					
					<OutputProfile>
						<Name>2k_profile</Name>
						<OutputStreamName>${OriginStreamName}_2k</OutputStreamName>

						<Playlist>
							<Name>2k</Name>
							<FileName>master_2k</FileName>
							<Options>
								<WebRtcAutoAbr>true</WebRtcAutoAbr>
								<HLSChunklistPathDepth>0</HLSChunklistPathDepth>
								<EnableTsPackaging>true</EnableTsPackaging>
							</Options>
							<RenditionTemplate>
								<Name>2k</Name>
								<VideoTemplate>
									<EncodingType>encoded</EncodingType>
								</VideoTemplate>
								<AudioTemplate>
									<EncodingType>encoded</EncodingType>
								</AudioTemplate>
							</RenditionTemplate>
						</Playlist>

						<Encodes>
							<Video>
								<Name>video_2k</Name>
								<Codec>h264</Codec>
								<Bitrate>8000000</Bitrate>
								<Width>2560</Width>
								<Height>1440</Height>
								<Framerate>30</Framerate>
								<KeyFrameInterval>30</KeyFrameInterval>
								<BFrames>0</BFrames>
								<Preset>faster</Preset>
							</Video>
							<Audio>
								<Name>aac_audio_2k</Name>
								<Codec>aac</Codec>
								<Bitrate>128000</Bitrate>
								<Samplerate>48000</Samplerate>
								<Channel>2</Channel>
							</Audio>
						</Encodes>
					</OutputProfile>
					

					<!-- FUTURE PROFILE: 4K for ultra-high-quality streaming (commented out for easy activation) -->
					<!--
					<OutputProfile>
						<Name>4k_profile</Name>
						<OutputStreamName>${OriginStreamName}_4k</OutputStreamName>

						<Playlist>
							<Name>4k</Name>
							<FileName>master_4k</FileName>
							<Options>
								<WebRtcAutoAbr>true</WebRtcAutoAbr>
								<HLSChunklistPathDepth>0</HLSChunklistPathDepth>
								<EnableTsPackaging>true</EnableTsPackaging>
							</Options>
							<RenditionTemplate>
								<Name>4k</Name>
								<VideoTemplate>
									<EncodingType>encoded</EncodingType>
								</VideoTemplate>
								<AudioTemplate>
									<EncodingType>encoded</EncodingType>
								</AudioTemplate>
							</RenditionTemplate>
						</Playlist>

						<Encodes>
							<Video>
								<Name>video_4k</Name>
								<Codec>h264</Codec>
								<Bitrate>15000000</Bitrate>
								<Width>2160</Width>
								<Height>3840</Height>
								<Framerate>30</Framerate>
								<KeyFrameInterval>30</KeyFrameInterval>
								<BFrames>0</BFrames>
								<Preset>faster</Preset>
							</Video>
							<Audio>
								<Name>aac_audio_4k</Name>
								<Codec>aac</Codec>
								<Bitrate>128000</Bitrate>
								<Samplerate>48000</Samplerate>
								<Channel>2</Channel>
							</Audio>
						</Encodes>
					</OutputProfile>
					-->

					<!-- BYPASS PROFILE: For when input stream already matches desired quality -->
					<OutputProfile>
							<Name>bypass_stream</Name>
							<OutputStreamName>${OriginStreamName}</OutputStreamName>

							<Playlist>
								<Name>default</Name>
								<FileName>master</FileName>
								<Options>
									<WebRtcAutoAbr>true</WebRtcAutoAbr>
									<HLSChunklistPathDepth>0</HLSChunklistPathDepth>
									<EnableTsPackaging>true</EnableTsPackaging>
								</Options>
								<RenditionTemplate>
									<Name>${Height}p</Name>
									<VideoTemplate>
										<EncodingType>all</EncodingType>
									</VideoTemplate>
									<AudioTemplate>
										<EncodingType>all</EncodingType>
									</AudioTemplate>
								</RenditionTemplate>
							</Playlist>

							<Encodes>
								<Video>
									<Name>bypass_video</Name>
									<Bypass>true</Bypass>
								</Video>
								<Audio>
									<Name>aac_audio</Name>
									<Codec>aac</Codec>
									<Bitrate>128000</Bitrate>
									<Samplerate>48000</Samplerate>
									<Channel>2</Channel>
									<BypassIfMatch>
										<Codec>eq</Codec>
									</BypassIfMatch>
								</Audio>
								<Audio>
									<Name>opus_audio</Name>
									<Codec>opus</Codec>
									<Bitrate>128000</Bitrate>
									<Samplerate>48000</Samplerate>
									<Channel>2</Channel>
									<BypassIfMatch>
										<Codec>eq</Codec>
									</BypassIfMatch>
								</Audio>
								<!--
								<Video>
									<Name>video_1280</Name>
									<Codec>h264</Codec>
									<Bitrate>5024000</Bitrate>
									<Width>1920</Width>
									<Height>1080</Height>
									<Framerate>30</Framerate>
									<KeyFrameInterval>30</KeyFrameInterval>
									<BFrames>0</BFrames>
									<Preset>faster</Preset>
								</Video>
								<Video>
									<Name>video_720</Name>
									<Codec>h264</Codec>
									<Bitrate>2024000</Bitrate>
									<Width>1280</Width>
									<Height>720</Height>
									<Framerate>30</Framerate>
									<KeyFrameInterval>30</KeyFrameInterval>
									<BFrames>0</BFrames>									
									<Preset>faster</Preset>
								</Video>	
								<Audio>
									<Name>bypass_audio</Name>
									<Bypass>true</Bypass>
								</Audio>																
								-->
								<!-- 
									 If all conditions of BypassIfMatch are matched, bypass is used without encoding.
									 [Options] 
									   eq: euqal to
									   lte: less than or equal to
									   gte: greater than or equal to

								<Video>
									<Name>conditional_video_encoding</Name>
									<Codec>h264</Codec>
									<Bitrate>2024000</Bitrate>
									<Width>1280</Width>
									<Height>720</Height>
									<Framerate>30</Framerate>
									<BypassIfMatch>
										<Codec>eq</Codec>
										<Framerate>lte</Framerate>
										<Width>lte</Width>
										<Height>lte</Height>
										<SAR>eq</SAR>
									</BypassIfMatch>									
								</Video>
								<Audio>
									<Name>conditional_audio_encoding</Name>
									<Codec>aac</Codec>
									<Bitrate>128000</Bitrate>
									<Samplerate>48000</Samplerate>
									<Channel>2</Channel>
									<BypassIfMatch>
										<Codec>eq</Codec>
										<Samplerate>lte</Samplerate>
										<Channel>eq</Channel>
									</BypassIfMatch>
								</Audio>																			
								-->
							</Encodes>
						</OutputProfile>
					</OutputProfiles>
					<Providers>
						<!-- OVT provider commented out for LLHLS optimization -->
						<!-- <OVT /> -->
						<WebRTC />
						<RTMP />
						<!-- SRT provider commented out for LLHLS optimization -->
						<!-- <SRT /> -->
						<!-- MPEGTS provider commented out for LLHLS optimization -->
						<!--
						<MPEGTS>
							<StreamMap>
						
									Set the stream name of the client connected to the port to "stream_${Port}"
									For example, if a client connects to port 4000, OME creates a "stream_4000" stream
									<Stream>
										<Name>stream_${Port}</Name>
										<Port>4000,4001-4004</Port>
									</Stream>
									<Stream>
										<Name>stream_4005</Name>
										<Port>4005</Port>
									</Stream>
							
								<Stream>
									<Name>stream_${Port}</Name>
									<Port>4000</Port>
								</Stream>
							</StreamMap>
						</MPEGTS>
						-->
						<RTSPPull />
						<WebRTC>
							<Timeout>30000</Timeout>
							<CrossDomains>
								<Url>*</Url>
							</CrossDomains>
						</WebRTC>

						<Schedule>
							<MediaRootDir>/opt/ovenmediaengine/media</MediaRootDir>
							<ScheduleFilesDir>/opt/ovenmediaengine/media</ScheduleFilesDir>
						</Schedule>
					</Providers>
					<Publishers>
						<AppWorkerCount>1</AppWorkerCount>
						<StreamWorkerCount>8</StreamWorkerCount>
						<!-- OVT publisher commented out for LLHLS optimization -->
						<!-- <OVT /> -->
						<WebRTC>
							<Timeout>30000</Timeout>
							<Rtx>false</Rtx>
							<Ulpfec>false</Ulpfec>
							<JitterBuffer>false</JitterBuffer>
							<CreateDefaultPlaylist>true</CreateDefaultPlaylist>
						</WebRTC>
						<LLHLS>
							<ChunkDuration>0.5</ChunkDuration>
							<!-- <PartHoldBack> SHOULD be at least three times the <ChunkDuration> -->
							<PartHoldBack>1.5</PartHoldBack>
							<SegmentDuration>6</SegmentDuration>
							<SegmentCount>10</SegmentCount>
							<DVR>
								<Enable>false</Enable>
								<TempStoragePath>/tmp/ome_dvr/</TempStoragePath>
								<MaxDuration>3600</MaxDuration>
							</DVR>
							<DRM>
								<Enable>false</Enable>
								<InfoFile>path/to/file.xml</InfoFile>
							</DRM>
							<CrossDomains>
								<Url>*</Url>
							</CrossDomains>
							<CreateDefaultPlaylist>true</CreateDefaultPlaylist> <!-- llhls.m3u8 -->
						</LLHLS>
						<HLS>
							<SegmentCount>4</SegmentCount>
							<SegmentDuration>4</SegmentDuration>
							<!-- <DVR>
								<Enable>true</Enable>
								<EventPlaylistType>false</EventPlaylistType>
								<TempStoragePath>/tmp/ome_dvr/</TempStoragePath>
								<MaxDuration>600</MaxDuration>
							</DVR> -->
							<CrossDomains>
								<Url>*</Url>
							</CrossDomains>
							<CreateDefaultPlaylist>true</CreateDefaultPlaylist> <!-- ts:playlist.m3u8 -->
						</HLS>

						<!--
						<Push>
							<StreamMap>
								<Enable>false</Enable>
								<Path>path/to/file.xml</Path>
							</StreamMap>
						</Push>
						<FILE>
							<RootPath>/prefix/path/to</RootPath>
							<StreamMap>
								<Enable>false</Enable>
								<Path>path/to/file.xml</Path>
							</StreamMap>
						</FILE>
						<Thumbnail>
							<CrossDomains>
								<Url>*</Url>
							</CrossDomains>
						</Thumbnail>						
						-->
					</Publishers>
				</Application>
			</Applications>
		</VirtualHost>
	</VirtualHosts>
</Server>