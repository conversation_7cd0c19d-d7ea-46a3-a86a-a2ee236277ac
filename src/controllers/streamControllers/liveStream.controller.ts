import { Request, Response } from "express";
import { STATUS_CODES } from "../../constants/statusCodes";
import { LIVE_STREAM_MESSAGES } from "../../constants/responseMessage";
import {
  createLiveStreamChannel,
  stopLiveStreamChannel,
  updateLiveStreamStatus,
} from "../../models/stream/stream.model";
import {
  calculateVideoLengthFromM3U8,
  ErrorResponse,
  SuccessResponse,
} from "../../utils/helper";
import { streamIO } from "../../sockets/stream";
import { LiveStreamService } from "../../services";
import { live2Service } from "../../services/streamService/live2.service";
/**
 * Following function is responsible to initiate live stream
 * @param req
 * @param res
 */
export const httpUserLiveStreaming = async (req: Request, res: Response) => {
  const userId = req.user?._id;
  try {
    const result = await createLiveStreamChannel(req.body, userId);

    if (!result) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        LIVE_STREAM_MESSAGES.INVALID_INPUT,
        []
      );
    }

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      LIVE_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      []
    );
  }
};

/**
 * Following controller is responsible to cancel live stream which just got initiated.
 * @param req
 * @param res
 */
export const httpCancelLivesStream = async (req: Request, res: Response) => {
  const streamKey = req.body.streamKey;
  try {
    const result = await stopLiveStreamChannel(streamKey);

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      LIVE_STREAM_MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

/**
 * Following function updates the stream status to live and isLive to true the moment user starts streaming.
 * @param req We accept streamkey = <userId>_<liveStreamId>
 * @param res Success response for starting the stream.
 * @returns result object of the function.
 */
export const handleStreamStart = async (req: Request, res: Response) => {
  const { name: streamKey } = req.body;

  try {
    // 1. Immediately authorize the stream with a 200 OK response
    // This prevents NGINX from disconnecting due to slow database operations.
    res.status(STATUS_CODES.OK).send("Stream authorized");

    // 2. Asynchronously update the stream status in the database
    // This operation now happens in the background without blocking the stream.
    const updatedStream = await updateLiveStreamStatus(streamKey, "live", true);
    
    if (!updatedStream) {
      console.error(`Stream not found or failed to update for streamKey: ${streamKey}`);
      // Even if the DB update fails, the stream is already authorized.
      // We log the error for debugging but don't interfere with the live connection.
      return;
    }

    // 3. Emit a socket event to notify clients that the stream has started
    streamIO.emit("streamStarted", { streamKey, updatedStream });

  } catch (error) {
    console.log(`Error starting stream for streamKey ${streamKey}:`, error);
    // The response has already been sent, so we just log the error.
  }
};

/**
 * Following function updates the stream status to "ended" and isLive = false. As soon as user stops the stream
 * @param req we accept streamKey = <userId>_<liveStreamId>
 * @param res we provide the success response for ending the stream.
 * @returns response
 */
export const handleStreamEnd = async (req: Request, res: Response) => {
  const { name: streamKey } = req.body;
  try {
    const videoLength = await calculateVideoLengthFromM3U8(streamKey);
    const updatedStream = await updateLiveStreamStatus(
      streamKey,
      "ended",
      false
    );

    if (!updatedStream) {
      return res
        .status(STATUS_CODES.NOT_FOUND)
        .json({ success: false, message: LIVE_STREAM_MESSAGES.NOT_FOUND });
    }
    res
      .status(STATUS_CODES.OK)
      .json({ success: true, message: LIVE_STREAM_MESSAGES.ENDED });
  } catch (error) {
    console.error("Error ending stream:", error);
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      sucess: false,
      message: LIVE_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
    });
  }
};

export const recordComplete = async (req: Request, res: Response) => {
  console.log("On-Play");
  console.log(req.body);
  res.status(200).json({ success: true });
};
export const recordReady = async (req: Request, res: Response) => {
  console.log("on_play-done");

  res.status(200).json({ success: true });
};

/**
 * Controller function to fetch Current live streams
 * @param req
 * @param res
 * @returns
 */
export const httpfetchLiveStreams = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await LiveStreamService.getCurrentLiveStreams(page, limit);

    if (result.success) {
      return SuccessResponse(
        res,
        result.statusCode,
        result.success,
        result.message,
        result.data
      );
    } else {
      return ErrorResponse(
        res,
        result.statusCode,
        result.success,
        result.message,
        null
      );
    }
  } catch (error) {
    console.error("Error in getLiveStreams:", error);
    return ErrorResponse(res, 500, false, "Internal Server Error", null);
  }
};

export const isStreamLive = async (req: Request, res: Response) => {
  try {
    const streamId = req.params.streamId;
    if (!streamId) {
      return ErrorResponse(res, 400, false, "StreamId required");
    }
    
    // Check with live2 service first for real-time status
    const isLiveFromLive2 = await live2Service.isStreamLive(streamId);
    
    if (isLiveFromLive2) {
      return SuccessResponse(res, 200, true, "Stream is live", {
        isLive: true,
        streamKey: streamId,
        hlsUrl: live2Service.getMasterPlaylistUrl(streamId),
        liveUrls: live2Service.getAllQualityUrls(streamId),
        availableQualities: live2Service.getAvailableQualities(),
        type: "llhls"
      });
    }
    
    // Fallback to database check
    const stream = await LiveStreamService.checkStreamIsLive(streamId);

    return SuccessResponse(
      res,
      stream.statusCode,
      stream.success,
      stream.message,
      stream.data
    );
  } catch (error) {
    return ErrorResponse(res, 500, false, "Internal sever error", null);
  }
};

/**
 * Get streaming statistics from live2 container
 */
export const getStreamingStats = async (req: Request, res: Response) => {
  try {
    const stats = await live2Service.getStreamingStats();
    
    if (!stats) {
      return ErrorResponse(res, 503, false, "Streaming server unavailable", null);
    }

    return SuccessResponse(res, 200, true, "Stats retrieved successfully", stats);
  } catch (error) {
    return ErrorResponse(res, 500, false, "Failed to get streaming stats", null);
  }
};

/**
 * Get LLHLS URLs for a stream with all available qualities including AUTO
 */
export const getStreamUrls = async (req: Request, res: Response) => {
  try {
    const { streamKey } = req.params;

    if (!streamKey) {
      return ErrorResponse(res, 400, false, "Stream key required", null);
    }

    const liveUrls = live2Service.getAllQualityUrls(streamKey);
    const availableQualities = live2Service.getAvailableQualities();

    const urls = {
      master: live2Service.getMasterPlaylistUrl(streamKey),
      rtmp: live2Service.generateRTMPUrl(streamKey),
      hls: live2Service.getHLSUrl(streamKey), // Legacy support
      liveUrls: liveUrls,
      availableQualities: availableQualities,
      type: "llhls"
    };

    return SuccessResponse(res, 200, true, "Stream URLs generated", urls);
  } catch (error) {
    return ErrorResponse(res, 500, false, "Failed to generate stream URLs", null);
  }
};

/**
 * Health check for the streaming infrastructure
 */
export const healthCheck = async (req: Request, res: Response) => {
  try {
    const isHealthy = await live2Service.healthCheck();

    if (isHealthy) {
      return SuccessResponse(res, 200, true, "Streaming server is healthy", {
        status: "healthy",
        timestamp: new Date().toISOString()
      });
    } else {
      return ErrorResponse(res, 503, false, "Streaming server is unhealthy", {
        status: "unhealthy",
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    return ErrorResponse(res, 500, false, "Health check failed", null);
  }
};

/**
 * Handle OvenMediaEngine AdmissionWebhooks
 * Single endpoint that handles both opening/closing events based on request.status
 * Dispatches internally to existing start/end logic
 * @param req OvenMediaEngine webhook payload
 * @param res Response to OME
 */
export const handleOmeWebhook = async (req: Request, res: Response) => {
  console.log("🔥 WEBHOOK HANDLER CALLED! 🔥");
  try {
    // Log the complete incoming request for debugging
    console.log("=== OME Webhook Debug Info ===");
    console.log("Request Method:", req.method);
    console.log("Request URL:", req.url);
    console.log("Request Headers:", JSON.stringify(req.headers, null, 2));
    console.log("Request Body:", JSON.stringify(req.body, null, 2));
    console.log("Request Query:", JSON.stringify(req.query, null, 2));
    console.log("Client IP:", req.ip || req.connection.remoteAddress);
    console.log("================================");

    const { client, request } = req.body;

    // Check if this is an OME webhook (has the expected structure)
    if (!client || !request || !request.status || !request.url) {
      console.error("OME Webhook validation failed:", {
        hasClient: !!client,
        hasRequest: !!request,
        hasStatus: !!(request && request.status),
        hasUrl: !!(request && request.url),
        receivedBody: req.body
      });
      return res.status(400).json({
        allowed: false,
        reason: "Invalid webhook payload"
      });
    }

    console.log("OME Webhook received:", {
      direction: request.direction,
      protocol: request.protocol,
      status: request.status,
      url: request.url,
      clientAddress: client.address
    });

    // Extract stream key from URL
    // OME URL format: rtmp://host:port/app/streamKey
    console.log("Extracting stream key from URL:", request.url);
    const urlParts = request.url.split('/');
    const streamKey = urlParts[urlParts.length - 1];
    console.log("URL parts:", urlParts);
    console.log("Extracted stream key:", streamKey);

    if (!streamKey) {
      console.error("No stream key found in OME webhook URL:", request.url);
      return res.status(400).json({ allowed: false, reason: "Invalid stream key" });
    }

    // Handle RTMP publishing events (case-insensitive)
    if (request.protocol.toLowerCase() === "rtmp" && request.direction === "incoming") {
      console.log(`Processing RTMP incoming event: ${request.status} for stream: ${streamKey}`);

      if (request.status === "opening") {
        // Stream start event - dispatch to existing start logic
        console.log(`OME Stream starting: ${streamKey}`);
        console.log("Creating mock request for handleStreamStart...");

        // Create a mock request object for the existing handleStreamStart function
        const mockReq = {
          body: { name: streamKey },
          user: null // OME doesn't provide user context
        } as Request;

        const mockRes = {
          status: (code: number) => ({
            send: (message: string) => {
              console.log(`Mock start response: ${code} - ${message}`);
            },
            json: (data: any) => {
              console.log(`Mock start response: ${code} - ${JSON.stringify(data)}`);
            }
          })
        } as Response;

        // Call existing start logic asynchronously
        console.log("Calling handleStreamStart...");
        handleStreamStart(mockReq, mockRes);

        // Respond to OME immediately
        console.log("Responding to OME with stream authorized");
        return res.status(200).json({
          allowed: true,
          reason: "Stream authorized"
        });

      } else if (request.status === "closing") {
        // Stream end event - dispatch to existing end logic
        console.log(`OME Stream ending: ${streamKey}`);
        console.log("Creating mock request for handleStreamEnd...");

        // Create a mock request object for the existing handleStreamEnd function
        const mockReq = {
          body: { name: streamKey },
          user: null
        } as Request;

        const mockRes = {
          status: (code: number) => ({
            send: (message: string) => {
              console.log(`Mock end response: ${code} - ${message}`);
            },
            json: (data: any) => {
              console.log(`Mock end response: ${code} - ${JSON.stringify(data)}`);
            }
          })
        } as Response;

        // Call existing end logic asynchronously
        console.log("Calling handleStreamEnd...");
        handleStreamEnd(mockReq, mockRes);

        // Respond to OME immediately
        console.log("Responding to OME with empty success response");
        return res.status(200).json({});
      } else {
        console.log(`OME RTMP event with unhandled status: ${request.status}`);
      }
    } else {
      console.log(`OME Non-RTMP or non-incoming event: ${request.protocol} ${request.direction} ${request.status}`);
    }

    // Other events (playback requests, non-RTMP, etc.) - just allow them
    console.log(`OME Other event: ${request.status} ${request.direction} ${request.protocol}`);
    console.log("Responding to OME with event allowed");
    return res.status(200).json({
      allowed: true,
      reason: "Event allowed"
    });

  } catch (error) {
    console.error("OME Webhook error:", error);
    console.error("Error stack:", error instanceof Error ? error.stack : 'No stack trace');
    return res.status(500).json({
      allowed: false,
      reason: "Internal server error"
    });
  }
};
